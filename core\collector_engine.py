#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集引擎模块
基于DrissionPage浏览器自动化 + 真实API调用实现商品数据采集
"""

import time
import json
import hashlib
import random
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import requests
from DrissionPage import ChromiumPage, ChromiumOptions
from utils.logger import get_logger


class CollectorEngine:
    """数据采集引擎 - 基于DrissionPage + 真实API"""

    def __init__(self, config_manager):
        """
        初始化采集引擎

        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.logger = get_logger(self.__class__.__name__)
        self.session = requests.Session()
        self.page = None
        self.is_collecting = False
        self.should_stop = False

        # Cookie存储文件
        self.cookie_file = Path("cookies.json")

        # API配置 - 使用真实的闲鱼API接口
        self.api_config = {
            'search_api': 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search/1.0/',
            'shop_api': 'https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list/1.0/',
            'detail_api': 'https://h5api.m.goofish.com/h5/mtop.taobao.idle.item.detail/1.0/',
            'user_info_api': 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.loginuser.get/1.0/'
        }

        # 基础参数配置 - 从网络监听中获取的真实参数
        self.base_params = {
            'jsv': '2.7.2',
            'appKey': '********',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.search.0.0'
        }

        # 设置请求头 - 模拟真实浏览器请求
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.goofish.com/',
            'Origin': 'https://www.goofish.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })

    def init_browser(self, headless: bool = False) -> bool:
        """
        初始化浏览器

        Args:
            headless: 是否使用无头模式

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 创建浏览器配置
            options = ChromiumOptions()

            if headless:
                options.headless()

            # 基础配置 - 使用DrissionPage 4.0的正确API
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-gpu')
            options.set_argument('--window-size', '1920,1080')
            options.set_argument('--disable-blink-features', 'AutomationControlled')
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-automation')
            options.set_argument('--test-type')
            options.set_argument('--disable-logging')
            options.set_argument('--silent')
            # 移除no-sandbox以避免安全警告

            # 用户代理 - 使用set_user_agent方法
            options.set_user_agent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )

            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)

            self.logger.info("浏览器初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False

    def save_cookies(self):
        """保存cookies到文件"""
        try:
            if self.page:
                cookies = self.page.cookies()
                with open(self.cookie_file, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)
                self.logger.info(f"Cookies已保存到 {self.cookie_file}")
        except Exception as e:
            self.logger.error(f"保存cookies失败: {e}")

    def save_complete_auth_data(self):
        """保存完整的认证数据，包括cookies、token和API参数"""
        try:
            if not self.page:
                self.logger.warning("浏览器未初始化，无法保存认证数据")
                return False

            # 获取所有cookies
            cookies = self.page.cookies()

            # 提取关键token信息
            token_info = self._extract_token_info(cookies)

            # 获取真实API参数
            api_params = self.get_real_api_params_from_browser()

            # 获取用户信息
            user_info = self._extract_user_info()

            # 构建完整的认证数据
            auth_data = {
                "timestamp": int(time.time() * 1000),
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "cookies": cookies,
                "token_info": token_info,
                "api_params": api_params,
                "user_info": user_info,
                "status": "active"
            }

            # 保存到JSON文件
            auth_file = Path("auth_data.json")
            with open(auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 完整认证数据已保存到 {auth_file}")
            self.logger.info(f"📊 保存信息:")
            self.logger.info(f"  - Cookies数量: {len(cookies)}")
            self.logger.info(f"  - Token信息: {len(token_info)} 项")
            self.logger.info(f"  - API参数: {len(api_params)} 项")
            self.logger.info(f"  - 用户信息: {user_info.get('nick', 'N/A')}")

            return True

        except Exception as e:
            self.logger.error(f"保存完整认证数据失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _extract_token_info(self, cookies: Dict[str, str]) -> Dict[str, Any]:
        """从cookies中提取所有token相关信息"""
        token_info = {}

        try:
            # 关键token字段
            key_tokens = [
                '_m_h5_tk',      # 主要token
                '_m_h5_tk_enc',  # 加密token
                '_tb_token_',    # 淘宝token
                't',             # 用户token
                'sgcookie',      # 安全cookie
                'cookie2',       # 二级cookie
                'x5sec',         # 安全验证
                'munb',          # 用户标识
                'uc1',           # 用户配置
                'uc3',           # 用户配置3
                'uc4',           # 用户配置4
                'tracknick',     # 用户昵称
                'lgc',           # 登录配置
                'dnk',           # 用户昵称
                'sn',            # 序列号
                'unb',           # 用户编号
                'uss',           # 用户会话
                'csg',           # 配置
                'l',             # 位置信息
                'isg',           # 实例组
                'tfstk',         # 防伪token
                'v',             # 版本
                'enc',           # 加密
                'hng',           # 杭州
                'uc2',           # 用户配置2
                'existShop',     # 店铺存在
                'publishItemObj', # 发布商品对象
                'sg',            # 安全组
                'cna',           # 客户端网络地址
                'sc',            # 安全码
                'xman_us_f',     # 用户标识
                'xlly_s',        # 会话
                'x',             # 未知
                'mt',            # 移动token
                'thw'            # 未知
            ]

            # 提取所有关键token
            for key in key_tokens:
                if key in cookies:
                    value = cookies[key]
                    token_info[key] = {
                        "value": value,
                        "length": len(value),
                        "type": self._analyze_token_type(key, value)
                    }

            # 特殊处理_m_h5_tk（主要token）
            if '_m_h5_tk' in cookies:
                m_h5_tk = cookies['_m_h5_tk']
                if '_' in m_h5_tk:
                    parts = m_h5_tk.split('_')
                    token_info['_m_h5_tk_parsed'] = {
                        "token_part": parts[0],
                        "timestamp_part": parts[1] if len(parts) > 1 else "",
                        "full_value": m_h5_tk
                    }

            # 添加统计信息
            token_info['_statistics'] = {
                "total_tokens": len(token_info) - 1,  # 减去统计信息本身
                "has_main_token": '_m_h5_tk' in cookies,
                "has_tb_token": '_tb_token_' in cookies,
                "has_user_token": 't' in cookies,
                "extraction_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            self.logger.error(f"提取token信息失败: {e}")
            token_info['_error'] = str(e)

        return token_info

    def _analyze_token_type(self, key: str, value: str) -> str:
        """分析token类型"""
        if not value:
            return "empty"

        if key == '_m_h5_tk' and '_' in value:
            return "main_token_with_timestamp"
        elif key in ['_tb_token_', 't']:
            return "user_token"
        elif key in ['_m_h5_tk_enc', 'enc']:
            return "encrypted_token"
        elif key in ['cookie2', 'sgcookie']:
            return "security_cookie"
        elif key in ['munb', 'unb']:
            return "user_identifier"
        elif key in ['tracknick', 'dnk']:
            return "user_nickname"
        elif value.isdigit():
            return "numeric_id"
        elif len(value) > 50:
            return "long_token"
        elif len(value) < 10:
            return "short_code"
        else:
            return "general_token"

    def _extract_user_info(self) -> Dict[str, Any]:
        """从页面中提取用户信息"""
        user_info = {}

        try:
            if not self.page:
                return user_info

            # 执行JavaScript获取用户信息
            js_code = """
            let userInfo = {};

            // 尝试从window对象获取用户信息
            if (window.g_config && window.g_config.userid) {
                userInfo.userId = window.g_config.userid;
            }

            // 尝试获取用户昵称
            if (window.g_config && window.g_config.nick) {
                userInfo.nick = window.g_config.nick;
            }

            // 从页面元素获取用户信息
            const userElements = document.querySelectorAll('[data-spm*="user"], .user-info, .username, .nick');
            for (let elem of userElements) {
                if (elem.textContent && elem.textContent.trim()) {
                    userInfo.displayName = elem.textContent.trim();
                    break;
                }
            }

            // 尝试从localStorage获取用户信息
            try {
                const localUser = localStorage.getItem('user') || localStorage.getItem('userInfo');
                if (localUser) {
                    const parsed = JSON.parse(localUser);
                    if (parsed.nick) userInfo.nick = parsed.nick;
                    if (parsed.userId) userInfo.userId = parsed.userId;
                }
            } catch(e) {}

            // 获取当前页面URL信息
            userInfo.currentUrl = window.location.href;
            userInfo.domain = window.location.hostname;

            return userInfo;
            """

            result = self.page.run_js(js_code)
            if result and isinstance(result, dict):
                user_info.update(result)

            # 从cookies中补充用户信息
            cookies = self.page.cookies()
            if 'tracknick' in cookies:
                user_info['tracknick'] = cookies['tracknick']
            if 'dnk' in cookies:
                user_info['dnk'] = cookies['dnk']
            if 'munb' in cookies:
                user_info['munb'] = cookies['munb']

            # 添加提取时间
            user_info['extraction_time'] = time.strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            self.logger.error(f"提取用户信息失败: {e}")
            user_info['_error'] = str(e)

        return user_info

    def load_complete_auth_data(self) -> Dict[str, Any]:
        """加载完整的认证数据"""
        try:
            auth_file = Path("auth_data.json")
            if not auth_file.exists():
                self.logger.info("未找到完整认证数据文件")
                return {}

            with open(auth_file, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)

            # 检查数据有效性
            if not isinstance(auth_data, dict):
                self.logger.warning("认证数据格式无效")
                return {}

            # 检查数据时效性（7天内有效）
            save_timestamp = auth_data.get('timestamp', 0)
            current_timestamp = int(time.time() * 1000)
            age_days = (current_timestamp - save_timestamp) / (1000 * 60 * 60 * 24)

            if age_days > 7:
                self.logger.warning(f"认证数据已过期（{age_days:.1f}天前保存）")
                return {}

            self.logger.info(f"✅ 成功加载认证数据（{age_days:.1f}天前保存）")
            self.logger.info(f"📊 数据信息:")
            self.logger.info(f"  - 保存时间: {auth_data.get('save_time', 'N/A')}")
            self.logger.info(f"  - Cookies数量: {len(auth_data.get('cookies', {}))}")
            self.logger.info(f"  - Token数量: {len(auth_data.get('token_info', {}))}")
            self.logger.info(f"  - 用户: {auth_data.get('user_info', {}).get('nick', 'N/A')}")

            return auth_data

        except Exception as e:
            self.logger.error(f"加载完整认证数据失败: {e}")
            return {}

    def apply_auth_data_to_session(self, auth_data: Dict[str, Any]) -> bool:
        """将认证数据应用到session"""
        try:
            if not auth_data:
                return False

            cookies = auth_data.get('cookies', {})
            if not cookies:
                self.logger.warning("认证数据中没有cookies")
                return False

            # 清除现有cookies
            self.session.cookies.clear()

            # 应用cookies到session
            for cookie_name, cookie_value in cookies.items():
                self.session.cookies.set(cookie_name, cookie_value)

            self.logger.info(f"✅ 已将 {len(cookies)} 个cookies应用到session")

            # 验证关键token
            token = self.get_token_from_cookies()
            if token:
                self.logger.info(f"✅ 成功获取token: {token[:20]}...")
                return True
            else:
                self.logger.warning("⚠️ 未能从cookies中获取有效token")
                return False

        except Exception as e:
            self.logger.error(f"应用认证数据失败: {e}")
            return False

    def load_cookies(self) -> bool:
        """从文件加载cookies"""
        try:
            if self.cookie_file.exists():
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 适配新的JSON格式
                if isinstance(data, dict) and 'cookies' in data:
                    cookies = data['cookies']
                else:
                    cookies = data

                if self.page:
                    # 先访问主页
                    self.page.get('https://www.goofish.com/')

                    # 添加cookies
                    for cookie_name, cookie_value in cookies.items():
                        try:
                            self.page.set.cookies({cookie_name: cookie_value})
                        except Exception as e:
                            self.logger.warning(f"添加cookie失败: {e}")

                    self.logger.info("Cookies加载成功")
                    return True
            else:
                self.logger.info("未找到cookies文件")
                return False
        except Exception as e:
            self.logger.error(f"加载cookies失败: {e}")
            return False

    def check_login_status(self, force_refresh: bool = False) -> bool:
        """
        检查登录状态

        Args:
            force_refresh: 是否强制刷新页面
        """
        try:
            if not self.page:
                return False

            # 只有在强制刷新或页面为空时才访问主页
            current_url = self.page.url
            if force_refresh or not current_url or 'goofish.com' not in current_url:
                self.page.get('https://www.goofish.com/')
                self.page.wait(3)

            # 检查是否有登录按钮（未登录状态）
            login_btn = self.page.ele('xpath://span[contains(text(), "登录")]', timeout=2)
            if login_btn:
                self.logger.info("检测到未登录状态")
                return False
            else:
                # 没有找到登录按钮，可能已登录
                # 再检查是否有用户头像或用户名
                user_avatar = self.page.ele('xpath://img[contains(@class, "avatar")]', timeout=2)
                user_info = self.page.ele('xpath://*[contains(@class, "user")]', timeout=2)

                if user_avatar or user_info:
                    self.logger.info("检测到已登录状态")
                    return True
                else:
                    self.logger.info("登录状态不明确，默认为未登录")
                    return False

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False

    def has_valid_cookies(self) -> bool:
        """
        检查是否有有效的cookies文件
        """
        try:
            # 检查cookies文件是否存在且不为空
            if self.cookie_file.exists():
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # 适配新的JSON格式
                    if isinstance(data, dict) and 'cookies' in data:
                        cookies = data['cookies']
                    else:
                        cookies = data

                    # 检查是否有核心的登录cookies
                    if cookies and len(cookies) > 3:
                        # 检查核心必需cookies
                        core_cookies = ['_m_h5_tk', '_tb_token_', 't']
                        has_core_cookies = all(key in cookies for key in core_cookies)
                        if has_core_cookies:
                            return True

            return False
        except Exception as e:
            self.logger.error(f"检查cookies失败: {e}")
            return False

    def wait_for_login(self, timeout: int = 300) -> bool:
        """
        等待用户手动登录

        Args:
            timeout: 超时时间（秒）

        Returns:
            bool: 是否登录成功
        """
        try:
            self.logger.info("🔑 请在浏览器中手动登录闲鱼...")
            self.logger.info(f"⏰ 等待登录完成，超时时间: {timeout}秒")

            start_time = time.time()
            check_count = 0

            while time.time() - start_time < timeout:
                # 每10秒检查一次登录状态，避免频繁刷新
                if check_count % 5 == 0:  # 每10秒检查一次 (5 * 2秒)
                    if self.check_login_status(force_refresh=False):
                        self.logger.info("✅ 登录成功！")

                        # 保存基础cookies
                        self.save_cookies()

                        # 保存完整认证数据
                        self.logger.info("💾 正在保存完整认证数据...")
                        if self.save_complete_auth_data():
                            self.logger.info("✅ 完整认证数据保存成功！")
                        else:
                            self.logger.warning("⚠️ 完整认证数据保存失败，但基础cookies已保存")

                        return True

                check_count += 1
                time.sleep(2)

            self.logger.warning("⏰ 登录超时")
            return False

        except Exception as e:
            self.logger.error(f"等待登录失败: {e}")
            return False

    def sync_cookies_to_session(self):
        """将浏览器cookies同步到requests session"""
        try:
            if self.page:
                cookies = self.page.cookies()
                cookie_count = 0
                # 核心必需cookies和可选cookies分开处理 - 基于网络监听发现的关键cookies
                core_cookies = ['_m_h5_tk', '_tb_token_', 't']
                optional_cookies = ['_m_h5_tk_enc', 'cookie2', 'sgcookie', 'x5secdata']

                for cookie_name, cookie_value in cookies.items():
                    self.session.cookies.set(cookie_name, cookie_value, domain='.goofish.com')
                    cookie_count += 1

                    # 记录重要的cookies
                    if cookie_name in core_cookies + optional_cookies:
                        self.logger.debug(f"重要Cookie {cookie_name}: {cookie_value[:20]}...")

                self.logger.info(f"🔄 已同步{cookie_count}个Cookies到session")

                # 检查核心cookies是否存在
                missing_core = [name for name in core_cookies if name not in cookies]
                missing_optional = [name for name in optional_cookies if name not in cookies]

                if missing_core:
                    self.logger.warning(f"缺少核心cookies: {missing_core}")
                elif missing_optional:
                    self.logger.info(f"缺少可选cookies: {missing_optional}，但仍可继续")

        except Exception as e:
            self.logger.error(f"同步cookies失败: {e}")

    def sync_cookies_from_file(self) -> bool:
        """从文件直接同步cookies到session（无需浏览器）"""
        try:
            if not self.cookie_file.exists():
                self.logger.warning("Cookies文件不存在")
                return False

            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 适配新的JSON格式
            if isinstance(data, dict) and 'cookies' in data:
                cookies = data['cookies']
            else:
                cookies = data

            if not cookies:
                self.logger.warning("Cookies文件为空")
                return False

            # 同步cookies到session
            cookie_count = 0
            # 核心必需cookies和可选cookies分开处理
            core_cookies = ['_m_h5_tk', '_tb_token_', 't']  # 核心必需
            optional_cookies = ['_m_h5_tk_enc', 'cookie2', 'sgcookie']  # 可选但重要

            for cookie_name, cookie_value in cookies.items():
                self.session.cookies.set(cookie_name, cookie_value, domain='.goofish.com')
                cookie_count += 1

                # 记录重要的cookies
                if cookie_name in core_cookies + optional_cookies:
                    self.logger.debug(f"重要Cookie {cookie_name}: {cookie_value[:20]}...")

            self.logger.info(f"🔄 从文件同步了{cookie_count}个Cookies到session")

            # 检查核心cookies是否存在
            missing_core = [name for name in core_cookies if name not in cookies]
            missing_optional = [name for name in optional_cookies if name not in cookies]

            if missing_core:
                self.logger.error(f"缺少核心cookies: {missing_core}")
                return False
            elif missing_optional:
                self.logger.warning(f"缺少可选cookies: {missing_optional}，但仍可尝试继续")

            return len(cookies) > 0

        except Exception as e:
            self.logger.error(f"从文件同步cookies失败: {e}")
            return False

    def get_token_from_cookies(self) -> str:
        """从cookies中获取token - 基于DrissionPage网络监听发现的真实token格式"""
        try:
            # 优先从浏览器获取最新的token
            if self.page:
                try:
                    browser_token = self.page.run_js("""
                        // 获取_m_h5_tk cookie
                        const cookies = document.cookie.split(';');
                        for (let cookie of cookies) {
                            const [name, value] = cookie.trim().split('=');
                            if (name === '_m_h5_tk') {
                                return value;
                            }
                        }
                        return '';
                    """)
                    if browser_token:
                        self.logger.debug(f"从浏览器获取token: {browser_token}")
                        return browser_token
                except Exception as e:
                    self.logger.debug(f"从浏览器获取token失败: {e}")

            # 从session cookies中获取
            m_h5_tk = self.session.cookies.get('_m_h5_tk', '')
            if m_h5_tk:
                self.logger.debug(f"从session cookies获取token: {m_h5_tk}")
                return m_h5_tk

            # 备用方案：从其他可能的cookie中获取
            for cookie_name in ['_tb_token_', 't', 'sgcookie']:
                cookie_value = self.session.cookies.get(cookie_name, '')
                if cookie_value:
                    self.logger.debug(f"使用备用token: {cookie_value[:20]}...")
                    return cookie_value

            self.logger.warning("未找到有效的token，可能需要登录")
            return ""

        except Exception as e:
            self.logger.error(f"获取token失败: {e}")
            return ""

    def get_real_api_params_from_browser(self) -> Dict[str, str]:
        """从浏览器中获取真实的API参数"""
        try:
            if not self.page:
                self.logger.warning("浏览器未初始化")
                return {}

            # 执行JavaScript获取页面中的真实参数
            js_code = """
            // 尝试获取页面中的API配置
            let apiParams = {};

            // 查找window对象中的配置
            if (window.g_config) {
                apiParams.appKey = window.g_config.appkey || window.g_config.appKey;
                apiParams.api = window.g_config.api;
            }

            // 查找mtop相关配置
            if (window.mtopjsonp) {
                apiParams.appKey = window.mtopjsonp.appkey;
            }

            // 查找其他可能的配置
            if (window._config) {
                apiParams.appKey = window._config.appkey || window._config.appKey;
            }

            // 从页面请求中提取参数
            if (window.performance && window.performance.getEntriesByType) {
                const resources = window.performance.getEntriesByType('resource');
                for (let resource of resources) {
                    if (resource.name.includes('mtop.taobao.idlehome') || resource.name.includes('h5api.m.goofish.com')) {
                        const url = new URL(resource.name);
                        if (url.searchParams.get('appKey')) {
                            apiParams.appKey = url.searchParams.get('appKey');
                            apiParams.api = url.searchParams.get('api');
                            apiParams.jsv = url.searchParams.get('jsv');
                            break;
                        }
                    }
                }
            }

            return apiParams;
            """

            result = self.page.run_js(js_code)
            if result and isinstance(result, dict):
                self.logger.info(f"从浏览器获取到API参数: {result}")
                return result
            else:
                self.logger.warning("未能从浏览器获取API参数")
                return {}

        except Exception as e:
            self.logger.error(f"从浏览器获取API参数失败: {e}")
            return {}

    def ensure_login(self) -> bool:
        """
        确保登录状态

        Returns:
            bool: 是否已登录
        """
        try:
            # 初始化浏览器
            if not self.page:
                if not self.init_browser():
                    return False

            # 尝试加载已保存的cookies
            if self.load_cookies():
                # 检查登录状态
                if self.check_login_status():
                    self.sync_cookies_to_session()
                    return True

            # 需要重新登录
            self.logger.info("🔑 需要重新登录")
            self.page.get('https://www.goofish.com/')

            # 等待用户登录
            if self.wait_for_login():
                self.sync_cookies_to_session()
                return True

            return False

        except Exception as e:
            self.logger.error(f"确保登录失败: {e}")
            return False


    
    def _generate_sign(self, data: str, timestamp: str, token: str = "", app_key: str = "********") -> str:
        """
        生成API签名 - 基于DrissionPage网络监听获取的真实闲鱼签名算法

        Args:
            data: 请求数据
            timestamp: 时间戳
            token: 用户token (从_m_h5_tk cookie中提取，格式如: 77b3d1313ea39ec76893592920aa1717_1753878327365)
            app_key: 应用密钥

        Returns:
            str: 签名
        """
        try:
            # 从网络监听中发现的真实签名算法
            # 通过分析成功请求发现签名格式为: {token_part}&{timestamp}&{app_key}&{data}
            if token and '_' in token:
                # token格式: 77b3d1313ea39ec76893592920aa1717_1753878327365
                # 只使用下划线前的部分作为签名
                token_part = token.split('_')[0]
            else:
                token_part = token or ""

            # 构建签名字符串 - 基于真实API请求的格式
            sign_str = f"{token_part}&{timestamp}&{app_key}&{data}"

            # 使用MD5生成签名
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()

            self.logger.debug(f"原始Token: {token}")
            self.logger.debug(f"Token部分: {token_part}")
            self.logger.debug(f"签名字符串: {sign_str}")
            self.logger.debug(f"生成签名: {sign}")

            return sign
        except Exception as e:
            self.logger.error(f"签名生成失败: {e}")
            # 备用简单签名
            return hashlib.md5(f"{timestamp}{data}".encode('utf-8')).hexdigest()
    
    def _build_search_params(self, keyword: str, page: int = 1) -> Dict[str, Any]:
        """
        构建搜索API参数 - 优先使用保存的认证数据

        Args:
            keyword: 搜索关键词
            page: 页码

        Returns:
            Dict: API参数
        """
        # 优先尝试加载保存的认证数据
        auth_data = self.load_complete_auth_data()

        # 如果有保存的认证数据，应用到session
        if auth_data:
            self.apply_auth_data_to_session(auth_data)
            saved_api_params = auth_data.get('api_params', {})
        else:
            saved_api_params = {}

        # 从浏览器获取实时API参数（如果浏览器可用）
        browser_params = self.get_real_api_params_from_browser()

        # 构建搜索数据 - 使用真实的搜索参数格式
        data = {
            "keyword": keyword,
            "pageNum": page,
            "pageSize": 20,
            "sortType": "default",
            "categoryId": "",
            "priceRange": "",
            "location": ""
        }

        data_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
        timestamp = str(int(time.time() * 1000))

        # 获取token
        token = self.get_token_from_cookies()

        # 参数优先级：浏览器实时参数 > 保存的参数 > 默认值
        app_key = browser_params.get('appKey') or saved_api_params.get('appKey', '********')
        jsv = browser_params.get('jsv') or saved_api_params.get('jsv', '2.7.2')
        api_name = browser_params.get('api') or saved_api_params.get('api', 'mtop.taobao.idlemtopsearch.pc.search')

        # 构建请求参数
        params = {
            'jsv': jsv,
            'appKey': app_key,
            't': timestamp,
            'sign': self._generate_sign(data_str, timestamp, token, app_key),
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': api_name,
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.search.0.0',
            'data': data_str
        }

        # 记录参数来源
        param_source = "浏览器实时" if browser_params else ("保存数据" if saved_api_params else "默认值")
        self.logger.info(f"使用API参数({param_source}) - appKey: {app_key}, api: {api_name}, jsv: {jsv}")
        self.logger.info(f"Token状态: {'有效' if token else '无效'} ({token[:20] + '...' if token else 'N/A'})")
        self.logger.debug(f"完整搜索参数: {params}")

        return params
    
    def _build_shop_params(self, shop_id: str, page: int = 1) -> Dict[str, Any]:
        """
        构建店铺API参数
        
        Args:
            shop_id: 店铺ID
            page: 页码
        
        Returns:
            Dict: API参数
        """
        data = {
            "userId": shop_id,
            "page": page,
            "pageSize": 20
        }
        
        data_str = json.dumps(data, separators=(',', ':'))
        
        timestamp = str(int(time.time() * 1000))

        params = {
            'jsv': '2.7.2',
            'appKey': '12574478',
            't': timestamp,
            'sign': self._generate_sign(data_str, timestamp),
            'api': 'mtop.idle.web.xyh.item.list',
            'v': '1.0',
            'type': 'originaljson',
            'dataType': 'json',
            'data': data_str
        }
        
        return params
    
    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析API响应 - 基于网络监听发现的真实响应格式

        Args:
            response_text: 响应文本

        Returns:
            Dict: 解析后的数据
        """
        try:
            # 直接解析JSON响应
            data = json.loads(response_text)

            # 检查响应状态
            if 'ret' in data:
                ret_codes = data['ret']
                if isinstance(ret_codes, list) and len(ret_codes) > 0:
                    ret_code = ret_codes[0]
                    self.logger.debug(f"API返回状态: {ret_code}")

                    # 检查是否成功
                    if 'SUCCESS' not in ret_code:
                        self.logger.warning(f"API调用失败: {ret_code}")

                        # 特殊错误处理
                        if 'TOKEN_EMPTY' in ret_code:
                            self.logger.error("Token为空，需要重新获取认证信息")
                        elif 'ILLEGAL_ACCESS' in ret_code:
                            self.logger.error("非法请求，可能需要更新签名算法")
                        elif 'NOT_EXISTED' in ret_code:
                            self.logger.warning("资源不存在或已下架")

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.error(f"响应内容: {response_text[:500]}...")
            return {}
        except Exception as e:
            self.logger.error(f"解析响应失败: {e}")
            self.logger.error(f"响应内容: {response_text[:500]}...")
            return {}
    
    def _extract_shop_id_from_url(self, shop_url: str) -> Optional[str]:
        """
        从店铺URL提取店铺ID
        
        Args:
            shop_url: 店铺URL
        
        Returns:
            Optional[str]: 店铺ID
        """
        try:
            # 从URL中提取用户ID
            # 例如: https://www.goofish.com/user/123456
            import re
            match = re.search(r'/user/(\d+)', shop_url)
            if match:
                return match.group(1)
            
            # 其他可能的URL格式
            match = re.search(r'userId=(\d+)', shop_url)
            if match:
                return match.group(1)
            
            return None
        except Exception as e:
            self.logger.error(f"提取店铺ID失败: {e}")
            return None

    def _extract_products_with_js(self, keyword: str, max_pages: int = 3, min_want_count: int = 10, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        使用JavaScript直接从页面提取商品数据，支持免登录模式

        Args:
            keyword: 搜索关键词
            max_pages: 最大页数

        Returns:
            List[Dict]: 商品数据列表
        """
        all_products = []

        try:
            # 如果浏览器未初始化，先初始化
            if not self.page:
                self.logger.info("浏览器未初始化，正在初始化...")
                if not self.init_browser(headless=True):  # 使用无头模式
                    self.logger.error("浏览器初始化失败")
                    return []

            # 导航到搜索页面（免登录）
            search_url = f"https://www.goofish.com/search?q={keyword}"
            self.logger.info(f"🌐 导航到搜索页面: {search_url}")
            self.page.get(search_url)

            for page_num in range(1, max_pages + 1):
                self.logger.info(f"提取第 {page_num} 页数据...")

                # 等待页面加载
                time.sleep(3)

                # 滑动到页面底部，确保所有商品都加载完成
                self._scroll_to_bottom_and_load_all()

                # 使用JavaScript提取商品数据
                js_code = """
                // 提取当前页面的所有商品信息
                function extractProducts() {
                    const products = [];

                    // 查找商品卡片的多种可能选择器
                    const selectors = [
                        '[class*="feeds-item"]',
                        '[class*="item-card"]',
                        '[class*="product-item"]',
                        '[class*="search-item"]',
                        '.item',
                        '[data-testid*="item"]'
                    ];

                    let itemElements = [];
                    for (let selector of selectors) {
                        itemElements = document.querySelectorAll(selector);
                        if (itemElements.length > 0) {
                            console.log(`找到商品元素，使用选择器: ${selector}, 数量: ${itemElements.length}`);
                            break;
                        }
                    }

                    if (itemElements.length === 0) {
                        console.log('未找到商品元素，尝试通用方法');
                        // 尝试查找包含价格信息的元素
                        itemElements = document.querySelectorAll('[class*="price"], [class*="Price"]');
                        if (itemElements.length > 0) {
                            // 向上查找父容器
                            const parentElements = new Set();
                            itemElements.forEach(el => {
                                let parent = el.closest('[class*="item"], [class*="card"], [class*="product"]');
                                if (parent) parentElements.add(parent);
                            });
                            itemElements = Array.from(parentElements);
                        }
                    }

                    itemElements.forEach((item, index) => {
                        try {
                            const product = {
                                id: '',
                                title: '',
                                price: '',
                                originalPrice: '',
                                wantCount: '',
                                browseCnt: '',
                                location: '',
                                sellerNick: '',
                                picUrl: '',
                                itemUrl: '',
                                publishTime: '',
                                status: 'normal'
                            };

                            // 提取标题
                            const titleSelectors = [
                                '[class*="title"]',
                                '[class*="Title"]',
                                'h1', 'h2', 'h3', 'h4',
                                '[class*="name"]',
                                'a[href*="/item"]'
                            ];

                            for (let selector of titleSelectors) {
                                const titleEl = item.querySelector(selector);
                                if (titleEl && titleEl.textContent.trim()) {
                                    product.title = titleEl.textContent.trim();
                                    break;
                                }
                            }

                            // 提取价格
                            const priceSelectors = [
                                '[class*="price"]',
                                '[class*="Price"]',
                                '[class*="money"]',
                                '[class*="cost"]'
                            ];

                            for (let selector of priceSelectors) {
                                const priceEl = item.querySelector(selector);
                                if (priceEl && priceEl.textContent.trim()) {
                                    const priceText = priceEl.textContent.trim();
                                    if (priceText.includes('¥') || priceText.includes('元') || /\d+/.test(priceText)) {
                                        product.price = priceText;
                                        break;
                                    }
                                }
                            }

                            // 提取想要人数
                            const wantSelectors = [
                                '[class*="want"]',
                                '[class*="Want"]',
                                '[class*="like"]',
                                '[class*="favor"]'
                            ];

                            for (let selector of wantSelectors) {
                                const wantEl = item.querySelector(selector);
                                if (wantEl && wantEl.textContent.trim()) {
                                    const wantText = wantEl.textContent.trim();
                                    if (/\d+/.test(wantText)) {
                                        product.wantCount = wantText;
                                        break;
                                    }
                                }
                            }

                            // 提取图片
                            const imgEl = item.querySelector('img');
                            if (imgEl && imgEl.src) {
                                product.picUrl = imgEl.src;
                            }

                            // 提取链接
                            const linkEl = item.querySelector('a[href*="/item"]');
                            if (linkEl && linkEl.href) {
                                product.itemUrl = linkEl.href;
                                // 从URL中提取ID
                                const idMatch = linkEl.href.match(/id=(\d+)/);
                                if (idMatch) {
                                    product.id = idMatch[1];
                                }
                            }

                            // 提取位置信息
                            const locationSelectors = [
                                '[class*="location"]',
                                '[class*="address"]',
                                '[class*="area"]'
                            ];

                            for (let selector of locationSelectors) {
                                const locationEl = item.querySelector(selector);
                                if (locationEl && locationEl.textContent.trim()) {
                                    product.location = locationEl.textContent.trim();
                                    break;
                                }
                            }

                            // 只有标题和价格都存在才认为是有效商品
                            if (product.title && product.price) {
                                products.push(product);
                            }

                        } catch (e) {
                            console.error('提取商品信息失败:', e);
                        }
                    });

                    return products;
                }

                return extractProducts();
                """

                try:
                    page_products = self.page.run_js(js_code)
                    if page_products and isinstance(page_products, list):
                        # 过滤数据：提取想要人数并转换为数字
                        filtered_products = []
                        for product in page_products:
                            want_count_str = product.get('wantCount', '0')
                            # 提取数字
                            import re
                            want_count_match = re.search(r'\d+', str(want_count_str))
                            want_count = int(want_count_match.group()) if want_count_match else 0
                            product['wantCount'] = want_count

                            # 应用过滤条件
                            if want_count >= min_want_count:
                                filtered_products.append(product)

                        self.logger.info(f"第 {page_num} 页提取到 {len(page_products)} 个商品，过滤后 {len(filtered_products)} 个")
                        all_products.extend(filtered_products)

                        # 调用进度回调
                        if progress_callback:
                            progress_callback({
                                'current_page': page_num,
                                'total_pages': max_pages,
                                'current_items': len(filtered_products),
                                'total_items': len(all_products),
                                'progress': int(page_num / max_pages * 100)
                            })

                        # 显示前几个商品信息用于调试
                        for i, product in enumerate(filtered_products[:3]):
                            self.logger.debug(f"商品 {i+1}: {product.get('title', 'N/A')[:30]}... - {product.get('price', 'N/A')} - 想要: {product.get('wantCount', 0)}")
                    else:
                        self.logger.warning(f"第 {page_num} 页未提取到商品数据")

                except Exception as e:
                    self.logger.error(f"第 {page_num} 页JavaScript执行失败: {e}")

                # 如果不是最后一页，尝试翻页
                if page_num < max_pages:
                    try:
                        # 先滑动到页面底部，确保分页按钮可见
                        self.logger.info(f"准备翻到第 {page_num + 1} 页，先滑动到底部...")
                        self._scroll_to_bottom_and_load_all()

                        # 查找下一页按钮并点击
                        next_button_js = """
                        // 查找下一页按钮
                        const nextSelectors = [
                            '[class*="next"]',
                            '[class*="Next"]',
                            'button:contains("下一页")',
                            'a:contains("下一页")',
                            '[aria-label*="下一页"]',
                            '.pagination .next',
                            '.page-next'
                        ];

                        for (let selector of nextSelectors) {
                            const nextBtn = document.querySelector(selector);
                            if (nextBtn && !nextBtn.disabled && nextBtn.style.display !== 'none') {
                                nextBtn.click();
                                return true;
                            }
                        }

                        // 尝试查找页码按钮
                        const pageButtons = document.querySelectorAll('[class*="page"] button, [class*="pagination"] button');
                        for (let btn of pageButtons) {
                            if (btn.textContent.trim() === String(""" + str(page_num + 1) + """)) {
                                btn.click();
                                return true;
                            }
                        }

                        return false;
                        """

                        clicked = self.page.run_js(next_button_js)
                        if clicked:
                            self.logger.info(f"成功翻到第 {page_num + 1} 页")
                            time.sleep(2)  # 等待页面加载
                        else:
                            self.logger.warning("未找到下一页按钮，停止翻页")
                            break

                    except Exception as e:
                        self.logger.error(f"翻页失败: {e}")
                        break

                # 添加页面间隔
                time.sleep(random.uniform(1, 2))

            self.logger.info(f"JavaScript提取完成，共获取 {len(all_products)} 个商品")
            return all_products

        except Exception as e:
            self.logger.error(f"JavaScript提取失败: {e}")
            return []

    def _extract_products_with_optimized_js(self, keyword: str, max_pages: int = 3, min_want_count: int = 10, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        使用优化的JavaScript直接从页面提取商品数据，基于真实DOM结构

        Args:
            keyword: 搜索关键词
            max_pages: 最大页数
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数

        Returns:
            List[Dict]: 商品数据列表
        """
        all_products = []

        try:
            # 如果浏览器未初始化，先初始化
            if not self.page:
                self.logger.info("浏览器未初始化，正在初始化...")
                if not self.init_browser(headless=True):  # 使用无头模式
                    self.logger.error("浏览器初始化失败")
                    return []

            # 导航到搜索页面（免登录）
            search_url = f"https://www.goofish.com/search?q={keyword}"
            self.logger.info(f"🌐 导航到搜索页面: {search_url}")
            self.page.get(search_url)

            for page_num in range(1, max_pages + 1):
                self.logger.info(f"提取第 {page_num} 页数据...")

                # 等待页面加载完成
                time.sleep(3)

                # 滑动到页面底部，确保所有商品都加载完成
                self._scroll_to_bottom_and_load_all()

                # 使用优化的JavaScript提取商品数据
                js_code = """
                // 基于真实DOM结构的优化提取函数
                function extractProductsOptimized() {
                    const products = [];

                    // 查找所有包含商品的a标签
                    const allLinks = document.querySelectorAll('a[href]');
                    const productLinks = Array.from(allLinks).filter(link =>
                        link.href.includes('/item/') ||
                        link.querySelector('img.feeds-image--TDRC4fV1')
                    );

                    console.log(`找到 ${productLinks.length} 个商品链接`);

                    productLinks.forEach((link, index) => {
                        try {
                            const product = {};

                            // 商品链接和ID
                            product.link = link.href;
                            if (product.link.includes('/item/')) {
                                const idMatch = product.link.match(/\\/item\\/(\\d+)\\.htm/) ||
                                               product.link.match(/id=(\\d+)/);
                                product.itemId = idMatch ? idMatch[1] : '';
                            }

                            // 商品图片
                            const imgElement = link.querySelector('img.feeds-image--TDRC4fV1');
                            product.imageUrl = imgElement ? (imgElement.src || imgElement.dataset.src) : '';

                            // 商品标题
                            const titleSpan = link.querySelector('span.main-title--sMrtWSJa');
                            if (titleSpan) {
                                product.title = titleSpan.textContent.trim();
                            } else {
                                // 从链接的完整文本中提取，去除价格等信息
                                const fullText = link.textContent.trim();
                                const lines = fullText.split('\\n').map(line => line.trim()).filter(line => line);
                                // 找到第一个不是价格、不是数字、不是符号的行作为标题
                                for (let line of lines) {
                                    if (line &&
                                        !line.match(/^[¥\\d\\.,]+$/) &&
                                        !line.includes('人想要') &&
                                        !line.includes('小时前') &&
                                        !line.includes('天内') &&
                                        line.length > 3) {
                                        product.title = line;
                                        break;
                                    }
                                }
                            }

                            // 商品价格
                            const priceSign = link.querySelector('.sign--x6uVdG3X');
                            const priceNumber = link.querySelector('.number--NKh1vXWM');
                            const priceDecimal = link.querySelector('.decimal--lSAcITCN');
                            const priceMagnitude = link.querySelector('.magnitude--EJxoo1DV');

                            if (priceSign && priceNumber) {
                                let price = priceSign.textContent + priceNumber.textContent;
                                if (priceDecimal) price += priceDecimal.textContent;
                                if (priceMagnitude) price += priceMagnitude.textContent;
                                product.price = price;
                            }

                            // 想要人数
                            const wantElements = link.querySelectorAll('div.text--MaM9Cmdn');
                            for (let element of wantElements) {
                                const text = element.textContent.trim();
                                if (text.includes('人想要')) {
                                    product.wantCount = text;
                                    break;
                                }
                            }

                            // 地区信息
                            const locationElement = link.querySelector('p.seller-text--Rr2Y3EbB');
                            product.location = locationElement ? locationElement.textContent.trim() : '';

                            // 卖家信用
                            const creditElement = link.querySelector('span.gradient-image-text--YUZj27iZ');
                            product.sellerCredit = creditElement ? creditElement.textContent.trim() : '';

                            // 时间戳
                            product.timestamp = new Date().toISOString();

                            // 只添加有效的商品（有标题或有价格）
                            if ((product.title && product.title.length > 3) || product.price) {
                                products.push(product);
                            }

                        } catch (error) {
                            console.log(`提取第${index}个商品时出错:`, error);
                        }
                    });

                    return products;
                }

                return extractProductsOptimized();
                """

                try:
                    page_products = self.page.run_js(js_code)
                    if page_products and isinstance(page_products, list):
                        # 过滤数据：提取想要人数并转换为数字
                        filtered_products = []
                        for product in page_products:
                            want_count_str = product.get('wantCount', '0')
                            # 提取数字
                            import re
                            want_count_match = re.search(r'\d+', str(want_count_str))
                            want_count = int(want_count_match.group()) if want_count_match else 0
                            product['wantCount'] = want_count

                            # 应用过滤条件
                            if want_count >= min_want_count:
                                filtered_products.append(product)

                        all_products.extend(filtered_products)
                        self.logger.info(f"第 {page_num} 页提取到 {len(filtered_products)} 个符合条件的商品")

                        # 更新进度
                        if progress_callback:
                            progress_callback({
                                'current_page': page_num,
                                'total_pages': max_pages,
                                'current_items': len(filtered_products),
                                'total_items': len(all_products),
                                'progress': int(page_num / max_pages * 100)
                            })

                        # 检查是否需要停止
                        if self.should_stop:
                            self.logger.info("收到停止信号，终止采集")
                            break

                    else:
                        self.logger.warning(f"第 {page_num} 页未提取到商品数据")

                except Exception as e:
                    self.logger.error(f"第 {page_num} 页JavaScript执行失败: {e}")
                    continue

                # 翻页处理
                if page_num < max_pages:
                    try:
                        # 先滑动到页面底部，确保分页按钮可见
                        self.logger.info(f"准备翻到第 {page_num + 1} 页，先滑动到底部...")
                        self._scroll_to_bottom_and_load_all()

                        # 点击下一页按钮
                        next_button_js = """
                        const nextButton = document.querySelector('.search-pagination-arrow-right--CKU78u4z') ||
                                         document.querySelector('[class*="next"]') ||
                                         document.querySelector('[class*="arrow-right"]') ||
                                         document.querySelector('button[class*="arrow-right"]');

                        if (nextButton && !nextButton.disabled && !nextButton.classList.contains('disabled')) {
                            // 滚动到按钮位置确保可见
                            nextButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 等待一下再点击
                            setTimeout(() => nextButton.click(), 500);
                            return true;
                        }
                        return false;
                        """

                        clicked = self.page.run_js(next_button_js)
                        if clicked:
                            self.logger.info(f"成功点击下一页按钮，等待页面加载...")
                            time.sleep(3)  # 等待页面加载
                        else:
                            self.logger.warning("未找到下一页按钮或按钮已禁用，停止翻页")
                            break

                    except Exception as e:
                        self.logger.error(f"翻页失败: {e}")
                        break

                # 添加页面间隔
                time.sleep(random.uniform(1, 2))

            self.logger.info(f"优化JavaScript提取完成，共获取 {len(all_products)} 个商品")
            return all_products

        except Exception as e:
            self.logger.error(f"优化JavaScript提取失败: {e}")
            return []

    def _extract_products_with_api_intercept(self, keyword: str, max_pages: int = 3, min_want_count: int = 10, progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        使用API拦截方式获取商品数据（简化版本，兼容DrissionPage 4.x）

        Args:
            keyword: 搜索关键词
            max_pages: 最大页数
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数

        Returns:
            List[Dict]: 商品数据列表
        """
        self.logger.warning("API拦截方式在当前DrissionPage版本中暂时不可用，跳过此方法")
        return []

    def _parse_api_product(self, item: Dict) -> Optional[Dict]:
        """
        解析API返回的商品数据

        Args:
            item: API返回的商品项

        Returns:
            Dict: 解析后的商品数据
        """
        try:
            product = {}

            # 基础字段
            product['itemId'] = str(item.get('id', ''))
            product['title'] = item.get('title', '').strip()
            product['price'] = item.get('price', '')
            product['wantCount'] = int(item.get('wantCnt', 0))
            product['browseCnt'] = int(item.get('browseCnt', 0))
            product['status'] = item.get('status', '在售')

            # 链接和图片
            product['link'] = f"https://www.goofish.com/item/{product['itemId']}.htm"
            product['imageUrl'] = item.get('picUrl', '')

            # 时间信息
            product['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 上架时间
            polish_time = item.get('proPolishTime')
            if polish_time:
                try:
                    if isinstance(polish_time, str) and polish_time.isdigit():
                        polish_time = int(polish_time)
                    if isinstance(polish_time, (int, float)):
                        product['publishTime'] = datetime.fromtimestamp(polish_time / 1000).strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        product['publishTime'] = str(polish_time)
                except:
                    product['publishTime'] = '未知'
            else:
                product['publishTime'] = '未知'

            # 计算转化率
            want_count = product['wantCount']
            browse_count = product['browseCnt']
            if browse_count > 0:
                product['conversionRate'] = round(want_count / browse_count * 100, 2)
            else:
                product['conversionRate'] = 0

            # 详细信息
            product['desc'] = item.get('desc', '').strip()
            product['favCnt'] = int(item.get('favCnt', 0))

            # 图片信息
            image_infos = item.get('imageInfos', [])
            if isinstance(image_infos, list) and image_infos:
                product['images'] = [img.get('url', '') for img in image_infos if isinstance(img, dict)]
            else:
                product['images'] = [product['imageUrl']] if product['imageUrl'] else []

            return product if product['title'] else None

        except Exception as e:
            self.logger.debug(f"解析商品数据失败: {e}")
            return None

    def _scroll_to_bottom_and_load_all(self):
        """
        滑动到页面底部，确保所有商品都加载完成
        使用渐进式滑动，模拟真实用户行为
        """
        try:
            self.logger.debug("开始滑动到页面底部...")

            # 渐进式滑动JavaScript代码 (修复async/await语法)
            scroll_js = """
            (async function() {
                function scrollToBottomGradually() {
                    return new Promise((resolve) => {
                        let totalHeight = 0;
                        let distance = 200; // 每次滑动距离
                        let scrollDelay = 300; // 滑动间隔
                        let maxScrolls = 50; // 最大滑动次数，防止无限滚动
                        let scrollCount = 0;

                        const timer = setInterval(() => {
                            const scrollHeight = document.body.scrollHeight;
                            window.scrollBy(0, distance);
                            totalHeight += distance;
                            scrollCount++;

                            // 检查是否到达底部或超过最大滑动次数
                            if (totalHeight >= scrollHeight || scrollCount >= maxScrolls) {
                                clearInterval(timer);

                                // 最后滚动到真正的底部
                                window.scrollTo(0, document.body.scrollHeight);

                                // 等待一下让页面稳定
                                setTimeout(() => {
                                    resolve({
                                        success: true,
                                        totalHeight: totalHeight,
                                        scrollHeight: scrollHeight,
                                        scrollCount: scrollCount
                                    });
                                }, 1000);
                            }
                        }, scrollDelay);
                    });
                }

                return await scrollToBottomGradually();
            })();
            """

            # 执行滑动
            result = self.page.run_js(scroll_js)

            if result and result.get('success'):
                self.logger.debug(f"滑动完成: 滑动次数={result.get('scrollCount')}, "
                                f"总高度={result.get('totalHeight')}, "
                                f"页面高度={result.get('scrollHeight')}")
            else:
                self.logger.warning("滑动结果异常，使用备用方法")
                # 备用滑动方法
                self._scroll_to_bottom_simple()

        except Exception as e:
            self.logger.warning(f"渐进式滑动失败: {e}，使用备用方法")
            # 备用滑动方法
            self._scroll_to_bottom_simple()

    def _scroll_to_bottom_simple(self):
        """
        简单的滑动到底部方法（备用）
        """
        try:
            simple_scroll_js = """
            (function() {
                // 简单滑动到底部
                window.scrollTo(0, document.body.scrollHeight);

                // 返回结果
                return { success: true, method: 'simple' };
            })();
            """

            self.page.run_js(simple_scroll_js)
            self.logger.debug("使用简单滑动方法完成")

        except Exception as e:
            self.logger.error(f"简单滑动也失败: {e}")

    def collect_search_data(self, keyword: str, max_pages: int = 3,
                          min_want_count: int = 10,
                          progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        采集搜索页面数据

        Args:
            keyword: 搜索关键词
            max_pages: 最大页数
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数

        Returns:
            List[Dict]: 采集到的商品数据
        """
        self.is_collecting = True
        self.should_stop = False
        collected_items = []

        try:
            self.logger.info(f"🚀 开始采集搜索数据: {keyword}")

            # 优先使用API拦截方式（最高效）
            self.logger.info("🚀 使用API拦截方式获取商品数据（最高效）...")
            api_products = self._extract_products_with_api_intercept(keyword, max_pages, min_want_count, progress_callback)
            if api_products:
                self.logger.info(f"✅ API拦截提取成功，获取到 {len(api_products)} 个商品")
                return api_products
            else:
                self.logger.warning("⚠️ API拦截失败，尝试优化JavaScript方法...")
                # 回退到优化JavaScript方法
                js_products = self._extract_products_with_optimized_js(keyword, max_pages, min_want_count, progress_callback)
                if js_products:
                    self.logger.info(f"✅ 优化JavaScript提取成功，获取到 {len(js_products)} 个商品")
                    return js_products
                else:
                    self.logger.warning("⚠️ 优化JavaScript提取失败，尝试原始方法...")
                    # 回退到原始方法
                    js_products = self._extract_products_with_js(keyword, max_pages, min_want_count, progress_callback)
                    if js_products:
                        self.logger.info(f"✅ 原始JavaScript提取成功，获取到 {len(js_products)} 个商品")
                        return js_products
                    else:
                        self.logger.error("❌ 所有提取方法都失败，无法获取商品数据")
                        return collected_items

        except Exception as e:
            self.logger.error(f"搜索采集失败: {e}")
            return collected_items
        finally:
            self.is_collecting = False
    
    def collect_shop_data(self, shop_url: str, max_items: int = 0,
                         min_want_count: int = 10,
                         progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        采集店铺数据
        
        Args:
            shop_url: 店铺URL
            max_items: 最大采集数量，0表示不限制
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数
        
        Returns:
            List[Dict]: 采集到的商品数据
        """
        self.is_collecting = True
        self.should_stop = False
        collected_items = []
        
        try:
            # 提取店铺ID
            shop_id = self._extract_shop_id_from_url(shop_url)
            if not shop_id:
                self.logger.error("无法从URL提取店铺ID")
                return []
            
            self.logger.info(f"开始采集店铺数据: {shop_id}")
            
            page = 1
            while True:
                if self.should_stop:
                    break
                
                # 构建请求参数
                params = self._build_shop_params(shop_id, page)
                
                # 发送请求
                try:
                    response = self.session.get(
                        self.api_config['shop_api'],
                        params=params,
                        timeout=15
                    )
                    
                    if response.status_code != 200:
                        self.logger.warning(f"第{page}页请求失败: {response.status_code}")
                        break
                    
                    # 解析响应
                    data = self._parse_response(response.text)
                    
                    if not data.get('data', {}).get('itemList'):
                        self.logger.info(f"第{page}页无更多数据，采集结束")
                        break
                    
                    # 处理商品数据
                    items = self._process_shop_items(data['data']['itemList'])
                    
                    # 过滤数据
                    filtered_items = [
                        item for item in items 
                        if item.get('wantCount', 0) >= min_want_count
                    ]
                    
                    collected_items.extend(filtered_items)
                    
                    # 检查是否达到最大数量
                    if max_items > 0 and len(collected_items) >= max_items:
                        collected_items = collected_items[:max_items]
                        break
                    
                    # 更新进度
                    if progress_callback:
                        progress_callback({
                            'current_page': page,
                            'total_pages': 999,  # 未知总页数
                            'current_items': len(filtered_items),
                            'total_items': len(collected_items),
                            'progress': min(page * 10, 90)  # 估算进度
                        })
                    
                    self.logger.info(f"第{page}页采集完成，获得{len(filtered_items)}条数据")
                    
                    # 如果当前页没有数据，结束采集
                    if not filtered_items:
                        break
                    
                    page += 1
                    time.sleep(random.uniform(1, 3))
                    
                except Exception as e:
                    self.logger.error(f"第{page}页采集失败: {e}")
                    break
            
            self.logger.info(f"店铺采集完成，共获得{len(collected_items)}条数据")
            return collected_items
            
        except Exception as e:
            self.logger.error(f"店铺采集失败: {e}")
            return collected_items
        finally:
            self.is_collecting = False

    def _process_search_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理搜索结果中的商品数据

        Args:
            items: 原始商品数据列表

        Returns:
            List[Dict]: 处理后的商品数据
        """
        processed_items = []

        for item in items:
            try:
                processed_item = {
                    'itemId': item.get('itemId', ''),
                    'title': item.get('title', ''),
                    'price': float(item.get('price', 0)),
                    'wantCount': int(item.get('wantCnt', 0)),
                    'browseCnt': int(item.get('browseCnt', 0)),
                    'status': '已售出' if item.get('isSold') else '在售',
                    'link': f"https://www.goofish.com/item/{item.get('itemId', '')}",
                    'imageUrl': item.get('picUrl', ''),
                    'publishTime': self._format_timestamp(item.get('proPolishTime')),
                    'location': item.get('location', ''),
                    'sellerNick': item.get('sellerNick', '')
                }

                # 计算转化率
                if processed_item['browseCnt'] > 0:
                    processed_item['conversionRate'] = round(
                        processed_item['wantCount'] / processed_item['browseCnt'] * 100, 2
                    )
                else:
                    processed_item['conversionRate'] = 0

                # 处理图片列表
                if item.get('imageInfos'):
                    processed_item['images'] = [img.get('url', '') for img in item['imageInfos']]
                else:
                    processed_item['images'] = [processed_item['imageUrl']] if processed_item['imageUrl'] else []

                processed_items.append(processed_item)

            except Exception as e:
                self.logger.warning(f"处理商品数据失败: {e}")
                continue

        return processed_items

    def _process_shop_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理店铺商品数据

        Args:
            items: 原始商品数据列表

        Returns:
            List[Dict]: 处理后的商品数据
        """
        # 店铺商品数据结构可能与搜索结果略有不同，但处理逻辑类似
        return self._process_search_items(items)

    def _format_timestamp(self, timestamp: Any) -> str:
        """
        格式化时间戳

        Args:
            timestamp: 时间戳

        Returns:
            str: 格式化后的时间字符串
        """
        try:
            if not timestamp:
                return '未知'

            if isinstance(timestamp, str) and timestamp.isdigit():
                timestamp = int(timestamp)

            if isinstance(timestamp, (int, float)):
                # 如果是毫秒时间戳，转换为秒
                if timestamp > 1e10:
                    timestamp = timestamp / 1000

                import datetime
                dt = datetime.datetime.fromtimestamp(timestamp)
                return dt.strftime('%Y-%m-%d %H:%M:%S')

            return str(timestamp)

        except Exception as e:
            self.logger.warning(f"时间戳格式化失败: {e}")
            return '未知'

    def stop_collection(self):
        """停止采集"""
        self.should_stop = True
        self.is_collecting = False
        self.logger.info("采集已停止")

    def login_with_small_browser(self) -> bool:
        """
        打开干净的小窗口浏览器进行登录

        Returns:
            bool: 是否成功打开浏览器
        """
        try:
            self.logger.info("🔑 启动登录浏览器...")

            # 关闭现有浏览器
            if self.page:
                self.page.quit()
                self.page = None

            # 创建干净的浏览器配置
            options = ChromiumOptions()

            # 窗口设置
            options.set_argument('--window-size', '900,700')
            options.set_argument('--window-position', '200,100')

            # 完全隐藏所有警告和提示
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-automation')
            options.set_argument('--disable-blink-features', 'AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_argument('--disable-features', 'VizDisplayCompositor')
            options.set_argument('--disable-extensions')
            options.set_argument('--disable-plugins')
            options.set_argument('--disable-browser-side-navigation')
            options.set_argument('--disable-dev-shm-usage')

            # 隐藏命令行标记警告
            options.set_argument('--test-type')
            options.set_argument('--ignore-certificate-errors')
            options.set_argument('--ignore-ssl-errors')
            options.set_argument('--ignore-certificate-errors-spki-list')
            options.set_argument('--disable-logging')
            options.set_argument('--disable-gpu-logging')
            options.set_argument('--silent')

            # 设置正常的用户代理
            options.set_user_agent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )

            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)

            # 打开闲鱼登录页面
            self.page.get('https://www.goofish.com/')

            # 等待页面加载完成
            import time
            time.sleep(3)

            # 直接获取并保存cookies
            self._get_and_save_cookies()

            self.logger.info("✅ 登录浏览器已启动")
            return True

        except Exception as e:
            self.logger.error(f"启动登录浏览器失败: {e}")
            return False

    def _get_and_save_cookies(self):
        """
        直接获取并保存当前页面的cookies
        """
        try:
            if not self.page:
                return False

            # 使用JavaScript获取所有cookies
            js_code = """
            return document.cookie.split(';').reduce((cookies, cookie) => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookies[name] = decodeURIComponent(value);
                }
                return cookies;
            }, {});
            """

            # 执行JavaScript获取cookies
            cookies = self.page.run_js(js_code)

            if cookies and len(cookies) > 0:
                # 保存cookies到JSON文件
                import time
                cookies_data = {
                    'login_time': time.time(),
                    'cookies': cookies,
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'url': 'https://www.goofish.com/'
                }

                with open(self.cookie_file, 'w', encoding='utf-8') as f:
                    json.dump(cookies_data, f, ensure_ascii=False, indent=2)

                self.logger.info(f"✅ Cookies已保存到 {self.cookie_file}")
                self.logger.info(f"📊 共保存 {len(cookies)} 个cookies")

                # 同步到session
                for cookie_name, cookie_value in cookies.items():
                    self.session.cookies.set(cookie_name, cookie_value)

                # 检查是否有登录相关的cookies
                key_cookies = ['_tb_token_', 'cookie2', 'sgcookie', 't']
                has_login_cookies = any(key in cookies for key in key_cookies)

                if has_login_cookies:
                    self.logger.info("✅ 检测到登录相关cookies")

                    # 保存完整认证数据
                    self.logger.info("💾 正在保存完整认证数据...")
                    if self.save_complete_auth_data():
                        self.logger.info("✅ 完整认证数据保存成功！")
                    else:
                        self.logger.warning("⚠️ 完整认证数据保存失败，但基础cookies已保存")

                    return True
                else:
                    self.logger.info("ℹ️ 未检测到登录相关cookies，可能需要登录")
                    return False
            else:
                self.logger.warning("⚠️ 未获取到任何cookies")
                return False

        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            return False

    def switch_to_headless_mode(self):
        """
        将当前浏览器切换到后台模式（最小化窗口）
        注意：DrissionPage无法动态切换到真正的无头模式，但可以最小化窗口
        """
        try:
            if self.page:
                # 最小化浏览器窗口
                self.page.run_js("window.minimize = function() { window.moveTo(-2000, -2000); window.resizeTo(1, 1); };")
                self.page.run_js("window.minimize();")
                self.logger.info("🔄 浏览器已切换到后台模式")
                return True
        except Exception as e:
            self.logger.warning(f"切换后台模式失败: {e}")
        return False

    def close_login_browser(self):
        """
        关闭登录浏览器
        """
        try:
            if self.page:
                # 关闭前再次获取cookies
                self._get_and_save_cookies()
                self.page.quit()
                self.page = None
                self.logger.info("🔒 登录浏览器已关闭")
        except Exception as e:
            self.logger.error(f"关闭登录浏览器失败: {e}")

    def init_headless_browser(self) -> bool:
        """
        初始化无头浏览器（用于采集）

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 关闭现有浏览器
            if self.page:
                self.page.quit()
                self.page = None

            # 创建无头浏览器配置
            options = ChromiumOptions()

            # 无头模式
            options.headless()

            # 基础配置（移除no-sandbox避免警告）
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-gpu')
            options.set_argument('--window-size', '1920,1080')
            options.set_argument('--disable-blink-features', 'AutomationControlled')
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-automation')
            options.set_argument('--test-type')
            options.set_argument('--disable-logging')
            options.set_argument('--silent')

            # 设置用户代理
            options.set_user_agent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )

            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)

            # 加载已保存的cookies
            if self.load_cookies():
                self.sync_cookies_to_session()
                self.logger.info("✅ 无头浏览器初始化成功，已加载登录状态")
                return True
            else:
                self.logger.warning("⚠️ 无头浏览器初始化成功，但未找到登录状态")
                return False

        except Exception as e:
            self.logger.error(f"无头浏览器初始化失败: {e}")
            return False

    def close(self):
        """关闭采集引擎"""
        self.stop_collection()

        # 关闭浏览器
        if self.page:
            try:
                self.page.quit()
                self.logger.info("🔒 浏览器已关闭")
            except Exception as e:
                self.logger.error(f"关闭浏览器失败: {e}")
            finally:
                self.page = None

        # 关闭session
        if self.session:
            self.session.close()
            self.logger.info("🔒 采集引擎已关闭")
